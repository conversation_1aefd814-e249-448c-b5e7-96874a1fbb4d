const fs = require('fs');
const path = require('path');

// Path to our JSON database file
const DB_FILE = path.join(__dirname, '..', 'users.json');

class User {
  // Get all users
  static getAll() {
    try {
      if (fs.existsSync(DB_FILE)) {
        const data = fs.readFileSync(DB_FILE, 'utf8');
        return JSON.parse(data);
      }
      return [];
    } catch (error) {
      console.error('Error reading users:', error);
      throw new Error('Failed to get users');
    }
  }

  // Find user by email
  static findByEmail(email) {
    const users = this.getAll();
    return users.find(user => user.email === email);
  }

  // Create a new user
  static create(userData) {
    try {
      const users = this.getAll();
      
      // Check if email already exists
      if (users.some(user => user.email === userData.email)) {
        throw new Error('Email already registered');
      }

      // Create new user object
      const newUser = {
        id: Date.now().toString(),
        email: userData.email,
        password: userData.password, // Note: In a real app, you should hash the password
        createdAt: new Date().toISOString()
      };

      // Add to users array
      users.push(newUser);

      // Write back to file
      fs.writeFileSync(DB_FILE, JSON.stringify(users, null, 2));

      // Return user without password
      const { password, ...userWithoutPassword } = newUser;
      return userWithoutPassword;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }
}

module.exports = User;
