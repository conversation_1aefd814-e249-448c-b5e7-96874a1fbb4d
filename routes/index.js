const express = require('express');
const router = express.Router();
const userRoutes = require('./userRoutes');
const statusRoutes = require('./statusRoutes');
const statusController = require('../controllers/statusController');

// Root route
router.get('/', statusController.getWelcome);

// Mount routes
router.use('/api/user', userRoutes);
router.use('/api', statusRoutes);

module.exports = router;
