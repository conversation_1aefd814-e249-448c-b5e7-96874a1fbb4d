const express = require('express');
const app = express();
const routes = require('./routes');
const logger = require('./middleware/logger');
const config = require('./config/config');

// Middleware to parse JSON bodies
app.use(express.json());

// Middleware to parse URL-encoded bodies
app.use(express.urlencoded({ extended: true }));

// Logger middleware
app.use(logger);

// Mount all routes
app.use(routes);

// Start the server
app.listen(config.port, () => {
  console.log(`Server is running on http://localhost:${config.port}`);
});
