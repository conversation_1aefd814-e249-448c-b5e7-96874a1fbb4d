const User = require('../models/User');

// Controller for user-related operations
const userController = {
  // Register a new user
  registerUser: (req, res) => {
    try {
      const { email, password } = req.body;

      // Basic validation
      if (!email || !password) {
        return res.status(400).json({ error: 'Email and password are required' });
      }

      // Simple email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ error: 'Invalid email format' });
      }

      // Simple password validation (at least 6 characters)
      if (password.length < 6) {
        return res.status(400).json({ error: 'Password must be at least 6 characters long' });
      }

      // Create user
      const newUser = User.create({ email, password });
      
      // Return success response
      res.status(201).json(newUser);
    } catch (error) {
      if (error.message === 'Email already registered') {
        return res.status(409).json({ error: error.message });
      }
      console.error('Error in registerUser controller:', error);
      res.status(500).json({ error: 'Failed to register user' });
    }
  }
};

module.exports = userController;
