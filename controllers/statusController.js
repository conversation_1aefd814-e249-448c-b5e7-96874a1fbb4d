// Controller for status-related endpoints
const statusController = {
  // Get API status
  getStatus: (req, res) => {
    res.json({
      status: 'online',
      timestamp: new Date(),
      serverInfo: {
        platform: process.platform,
        nodeVersion: process.version
      }
    });
  },

  // Hello world endpoint
  getHello: (req, res) => {
    res.json({ message: 'Hello World' });
  },

  // Welcome endpoint
  getWelcome: (req, res) => {
    res.json({ message: 'Welcome to the Express server!' });
  }
};

module.exports = statusController;
